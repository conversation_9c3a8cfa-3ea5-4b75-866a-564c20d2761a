<!DOCTYPE html>
<html lang="ar" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title> خليها ع الله</title>

  <!-- PageFlip CSS -->
  <link rel="stylesheet" href="./dist/js/stPageFlip.css" />

  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      background: linear-gradient(135deg, #e0f7fa, #80deea);
      font-family: 'Segoe UI', sans-serif;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      direction: ltr;
    }

    #flipbook-container {
      width: 100%;
      max-width: 1100px;
      height: 700px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
    }

    #flipbook {
      width: 100%;
      height: 100%;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .page {
      width: 100%;
      height: 100%;
      border-radius: 16px;
      overflow: hidden;
    }

    .page img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .cover, .back-cover {
      background: linear-gradient(135deg, #37474f, #263238);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 2rem;
      padding: 20px;
      border-radius: 16px;
    }

    .controls {
      display: flex;
      gap: 20px;
    }

    button {
      background: #00796b;
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 10px;
      font-size: 1rem;
      cursor: pointer;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    button:hover {
      background: #004d40;
      transform: translateY(-2px);
    }
  </style>
</head>
<body>

  <div id="flipbook-container">
    <div id="flipbook">
      <!-- الغلاف الأمامي -->
      <div class="page cover">
        <div>
         <h1 style="font-size: 3rem !important ; margin-top: 2px;">Senior 2025</h1>
          <p>Start Now</p>
        </div>
      </div>

      <!-- صفحات صور -->

      <div class="page"><img src="./pages/page5.jpg" alt="page 5"></div>
      <div class="page"><img src="./pages/page6.jpg" alt="page 6"></div>
      <div class="page"><img src="./pages/page7.jpg" alt="page 7"></div>
      <div class="page"><img src="./pages/page8.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page9.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page10.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page11.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page12.jpg" alt="page 8"></div>

      <!-- الغلاف الخلفي -->
      <div class="page back-cover">
        <div>
          <h1>The End</h1>
        </div>
      </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="controls">
      <button id="prev-btn"> Previous page</button>
            <button id="next-btn"> Next page</button>

      
    </div>
  </div>

  <!-- PageFlip JS -->
  <script src="./dist/js/page-flip.browser.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const flipbook = document.getElementById('flipbook');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      const pageFlip = new St.PageFlip(flipbook, {
        width: 500,
        height: 700,
        size: 'fixed',
        showCover: true,
        rtl: false, // فتح من اليسار لليمين
        useMouseEvents: true,
        maxShadowOpacity: 0.5,
        mobileScrollSupport: false,
        disableFlipByClick: false,
      });

      pageFlip.loadFromHTML(document.querySelectorAll('.page'));

      prevBtn.addEventListener('click', () => {
        pageFlip.flipPrev();
      });

      nextBtn.addEventListener('click', () => {
        pageFlip.flipNext();
      });

      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          pageFlip.flipPrev();
        } else if (e.key === 'ArrowRight') {
          pageFlip.flipNext();
        }
      });
    });
  </script>
</body>
</html>

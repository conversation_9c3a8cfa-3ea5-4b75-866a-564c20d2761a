<!DOCTYPE html>
<html lang="ar" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title> خليها ع الله</title>

  <!-- PageFlip CSS -->
  <link rel="stylesheet" href="./dist/js/stPageFlip.css" />

  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Poppins', sans-serif;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      direction: ltr;
      overflow: hidden;
      position: relative;
    }

    /* خلفية متحركة */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
      animation: backgroundMove 20s ease-in-out infinite;
      z-index: -1;
    }

    @keyframes backgroundMove {
      0%, 100% { transform: scale(1) rotate(0deg); }
      50% { transform: scale(1.1) rotate(180deg); }
    }

    #flipbook-container {
      width: 100%;
      max-width: 1200px;
      height: 750px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 30px;
      padding: 20px;
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow:
        0 25px 45px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    #flipbook {
      width: 100%;
      height: 100%;
      border-radius: 20px;
      overflow: hidden;
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 15px 12px rgba(0, 0, 0, 0.2);
      position: relative;
    }

    #flipbook::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
      border-radius: 22px;
      z-index: -1;
      animation: borderGlow 3s ease-in-out infinite alternate;
    }

    @keyframes borderGlow {
      0% { opacity: 0.5; transform: scale(1); }
      100% { opacity: 0.8; transform: scale(1.02); }
    }

    .page {
      width: 100%;
      height: 100%;
      border-radius: 20px;
      overflow: hidden;
      position: relative;
    }

    .page img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      transition: transform 0.3s ease;
    }

    .page:hover img {
      transform: scale(1.05);
    }

    .cover, .back-cover {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 2rem;
      padding: 40px;
      border-radius: 20px;
      position: relative;
      overflow: hidden;
    }

    .cover::before, .back-cover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .cover h1, .back-cover h1 {
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      margin-bottom: 10px;
    }

    .cover p {
      font-weight: 300;
      opacity: 0.9;
      font-size: 1.2rem;
    }

    .controls {
      display: flex;
      gap: 25px;
      align-items: center;
    }

    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 15px 35px;
      border-radius: 50px;
      font-size: 1.1rem;
      font-weight: 500;
      cursor: pointer;
      box-shadow:
        0 8px 15px rgba(0, 0, 0, 0.1),
        0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      font-family: 'Poppins', sans-serif;
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    button:hover::before {
      left: 100%;
    }

    button:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      transform: translateY(-3px);
      box-shadow:
        0 12px 20px rgba(0, 0, 0, 0.15),
        0 8px 10px rgba(0, 0, 0, 0.1);
    }

    button:active {
      transform: translateY(-1px);
      box-shadow:
        0 6px 10px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* تأثيرات إضافية للصفحات */
    .page {
      transition: all 0.3s ease;
    }

    .page::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .page:hover::after {
      opacity: 1;
    }

    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
      #flipbook-container {
        max-width: 95%;
        height: 600px;
        gap: 20px;
        padding: 15px;
      }

      button {
        padding: 12px 25px;
        font-size: 1rem;
      }

      .cover h1, .back-cover h1 {
        font-size: 2.5rem;
      }
    }
  </style>
</head>
<body>

  <div id="flipbook-container">
    <div id="flipbook">
      <!-- الغلاف الأمامي -->
      <div class="page cover">
        <div>
          <h1 style="font-size: 3.5rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Senior 2025</h1>
          <p style="font-size: 1.4rem; font-weight: 300; letter-spacing: 2px; opacity: 0.9;">✨ Start Your Journey ✨</p>
          <div style="margin-top: 20px; font-size: 0.9rem; opacity: 0.7;">
            <p>📖 Swipe to explore</p>
          </div>
        </div>
      </div>

      <!-- صفحات صور -->

      <div class="page"><img src="./pages/page5.jpg" alt="page 5"></div>
      <div class="page"><img src="./pages/page6.jpg" alt="page 6"></div>
      <div class="page"><img src="./pages/page7.jpg" alt="page 7"></div>
      <div class="page"><img src="./pages/page8.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page9.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page10.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page11.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page12.jpg" alt="page 8"></div>

      <!-- الغلاف الخلفي -->
      <div class="page back-cover">
        <div>
          <h1 style="font-size: 3rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">🎓 The End 🎓</h1>
          <p style="font-size: 1.2rem; font-weight: 300; opacity: 0.9; margin-bottom: 10px;">Thank you for the memories</p>
          <p style="font-size: 1rem; opacity: 0.7;">Senior Class of 2025</p>
        </div>
      </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="controls">
      <button id="prev-btn">
        <span style="margin-right: 8px;">⬅️</span>
        Previous Page
      </button>
      <div style="color: white; font-weight: 500; opacity: 0.8; font-size: 0.9rem;">
        Use arrow keys or click to navigate
      </div>
      <button id="next-btn">
        Next Page
        <span style="margin-left: 8px;">➡️</span>
      </button>
    </div>
  </div>

  <!-- PageFlip JS -->
  <script src="./dist/js/page-flip.browser.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const flipbook = document.getElementById('flipbook');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      const pageFlip = new St.PageFlip(flipbook, {
        width: 500,
        height: 700,
        size: 'fixed',
        showCover: true,
        rtl: false, // فتح من اليسار لليمين
        useMouseEvents: true,
        maxShadowOpacity: 0.5,
        mobileScrollSupport: false,
        disableFlipByClick: false,
      });

      pageFlip.loadFromHTML(document.querySelectorAll('.page'));

      // تأثيرات صوتية للأزرار (اختيارية)
      function playClickSound() {
        // يمكن إضافة صوت هنا إذا أردت
      }

      prevBtn.addEventListener('click', () => {
        pageFlip.flipPrev();
        playClickSound();
        // تأثير بصري للزر
        prevBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
          prevBtn.style.transform = '';
        }, 150);
      });

      nextBtn.addEventListener('click', () => {
        pageFlip.flipNext();
        playClickSound();
        // تأثير بصري للزر
        nextBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
          nextBtn.style.transform = '';
        }, 150);
      });

      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          pageFlip.flipPrev();
          prevBtn.style.transform = 'scale(0.95)';
          setTimeout(() => {
            prevBtn.style.transform = '';
          }, 150);
        } else if (e.key === 'ArrowRight') {
          pageFlip.flipNext();
          nextBtn.style.transform = 'scale(0.95)';
          setTimeout(() => {
            nextBtn.style.transform = '';
          }, 150);
        }
      });

      // تأثير تحميل الصفحة
      const container = document.getElementById('flipbook-container');
      container.style.opacity = '0';
      container.style.transform = 'translateY(50px)';

      setTimeout(() => {
        container.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
      }, 100);
    });
  </script>
</body>
</html>
